<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_6c674a54dec21c16e558a5d9336f6db5 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_6c674a54dec21c16e558a5d9336f6db5" ></div>
        
</body>
<script>
    
    
            var map_6c674a54dec21c16e558a5d9336f6db5 = L.map(
                "map_6c674a54dec21c16e558a5d9336f6db5",
                {
                    center: [20.0, -40.0],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 2,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_5efdbd4c83400d9d36e72bd52c0ce575 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_5efdbd4c83400d9d36e72bd52c0ce575.addTo(map_6c674a54dec21c16e558a5d9336f6db5);
        
    
            var marker_8bd97717dbf6db432e73550ee470ae93 = L.marker(
                [20.0, -40.0],
                {
}
            ).addTo(map_6c674a54dec21c16e558a5d9336f6db5);
        
    
        var popup_f68c07c0b01a95d83152028cda24ec6a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_934327544450eb9b639272a783e4b520 = $(`<div id="html_934327544450eb9b639272a783e4b520" style="width: 100.0%; height: 100.0%;">Default Location</div>`)[0];
                popup_f68c07c0b01a95d83152028cda24ec6a.setContent(html_934327544450eb9b639272a783e4b520);
            
        

        marker_8bd97717dbf6db432e73550ee470ae93.bindPopup(popup_f68c07c0b01a95d83152028cda24ec6a)
        ;

        
    
</script>
</html>