import streamlit as st
import requests
import numpy as np
import plotly.graph_objs as go

st.set_page_config(page_title="Marine Ship Control Room", layout="wide")
st.title("Marine Ship Control Room Dashboard")

# Simulate fetching data from backend (replace with actual API calls if available)
def fetch_data():
    # Example: Replace with requests.get('http://localhost:8050/api/data').json() for real backend
    return {
        'speed': np.random.randint(10, 30),
        'temperature': np.random.randint(-5, 35),
        'pressure': np.random.randint(980, 1020),
        'fan_rpm': np.random.randint(1000, 3000),
        'compass': np.random.randint(0, 360),
        'engine_temp': np.random.randint(60, 100),
        'humidity': np.random.randint(40, 90),
        'wind_speed': np.random.randint(5, 40),
        'battery': np.random.randint(20, 100)
    }

data = fetch_data()

# Radar simulation
with st.container():
    st.subheader("Radar")
    theta = np.linspace(0, 360, 36)
    r = np.random.uniform(0, 1, size=36)
    radar_fig = go.Figure(go.Scatterpolar(
        r=r,
        theta=theta,
        mode='lines',
        line=dict(color='lime', width=3)
    ))
    radar_fig.update_layout(
        polar=dict(bgcolor='black', radialaxis=dict(visible=False)),
        showlegend=False,
        margin=dict(l=20, r=20, t=20, b=20),
        paper_bgcolor='black',
        font=dict(color='lime')
    )
    st.plotly_chart(radar_fig, use_container_width=True)

# Gauges and sensors
cols = st.columns(6)
cols[0].metric("Speed (knots)", data['speed'])
cols[1].metric("Temperature (°C)", data['temperature'])
cols[2].metric("Pressure (hPa)", data['pressure'])
cols[3].metric("Fan RPM", data['fan_rpm'])
cols[4].metric("Compass (°)", data['compass'])
cols[5].metric("Engine Temp (°C)", data['engine_temp'])

cols2 = st.columns(3)
cols2[0].metric("Humidity (%)", data['humidity'])
cols2[1].metric("Wind Speed (kt)", data['wind_speed'])
cols2[2].metric("Battery (%)", data['battery'])

st.info("This Streamlit dashboard can be connected to your backend via API for live data.")
