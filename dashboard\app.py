import dash
from dash import dcc, html
import plotly.graph_objs as go
import pandas as pd
import numpy as np
import folium
import os
import networkx as nx

app = dash.Dash(__name__)

# Simulated data for gauges
speed = np.random.randint(10, 30)
temperature = np.random.randint(-5, 35)
pressure = np.random.randint(980, 1020)
fan_rpm = np.random.randint(1000, 3000)
compass = np.random.randint(0, 360)

# Additional sensors
engine_temp = np.random.randint(60, 100)
humidity = np.random.randint(40, 90)
wind_speed = np.random.randint(5, 40)
app.layout = html.Div([
    html.H1('Marine Time Weather Dashboard', style={
        'textAlign': 'center', 'color': '#00eaff', 'textShadow': '2px 2px 8px #000'
    }),
    html.Div([
        html.Div([
            html.H3('Radar', style={
                'color': 'lime', 'textAlign': 'center', 'textShadow': '1px 1px 6px #000'
            }),
            dcc.Graph(
                id='radar',
                figure=radar_figure(),
                style={'backgroundColor': 'black', 'height': '300px', 'boxShadow': '0 0 20px #00eaff'}
            ),
            html.Div([
                dcc.Graph(id='speedometer', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=speed, title={'text': "Speed (knots)"},
                    gauge={'axis': {'range': [0, 40]}}
                )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                dcc.Graph(id='temperature', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=temperature, title={'text': "Temperature (°C)"},
                    gauge={'axis': {'range': [-10, 40]}}
                )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #ff9800'}),
                dcc.Graph(id='pressure', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=pressure, title={'text': "Pressure (hPa)"},
                    gauge={'axis': {'range': [950, 1050]}}
                )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                dcc.Graph(id='fan_rpm', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=fan_rpm, title={'text': "Fan RPM"},
                    gauge={'axis': {'range': [0, 4000]}}
                )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                dcc.Graph(id='compass', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=compass, title={'text': "Compass (°)"},
                    gauge={'axis': {'range': [0, 360]}}
                )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                dcc.Graph(id='engine_temp', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=engine_temp, title={'text': "Engine Temp (°C)"},
                    gauge={'axis': {'range': [50, 120]}}
                )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #ff9800'}),
                dcc.Graph(id='humidity', figure=go.Figure(go.Indicator(
                    mode="gauge+number", value=humidity, title={'text': "Humidity (%)"},
                    gauge={'axis': {'range': [0, 100]}}
])
        'D': (35, -25)
    }
    # Add nodes    app.layout = html.Div([
        html.H1('Marine Time Weather Dashboard', style={
            'textAlign': 'center', 'color': '#00eaff', 'textShadow': '2px 2px 8px #000'
        }),
        html.Div([
            html.Div([
                html.H3('Radar', style={
                    'color': 'lime', 'textAlign': 'center', 'textShadow': '1px 1px 6px #000'
                }),
                dcc.Graph(
                    id='radar',
                    figure=radar_figure(),
                    style={'backgroundColor': 'black', 'height': '300px', 'boxShadow': '0 0 20px #00eaff'}
                ),
                html.Div([
                    dcc.Graph(id='speedometer', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=speed, title={'text': "Speed (knots)"},
                        gauge={'axis': {'range': [0, 40]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    dcc.Graph(id='temperature', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=temperature, title={'text': "Temperature (°C)"},
                        gauge={'axis': {'range': [-10, 40]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #ff9800'}),
                    dcc.Graph(id='pressure', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=pressure, title={'text': "Pressure (hPa)"},
                        gauge={'axis': {'range': [950, 1050]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    dcc.Graph(id='fan_rpm', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=fan_rpm, title={'text': "Fan RPM"},
                        gauge={'axis': {'range': [0, 4000]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    dcc.Graph(id='compass', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=compass, title={'text': "Compass (°)"},
                        gauge={'axis': {'range': [0, 360]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    dcc.Graph(id='engine_temp', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=engine_temp, title={'text': "Engine Temp (°C)"},
                        gauge={'axis': {'range': [50, 120]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #ff9800'}),
                    dcc.Graph(id='humidity', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=humidity, title={'text': "Humidity (%)"},
                        gauge={'axis': {'range': [0, 100]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    dcc.Graph(id='wind_speed', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=wind_speed, title={'text': "Wind Speed (kt)"},
                        gauge={'axis': {'range': [0, 50]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    dcc.Graph(id='battery', figure=go.Figure(go.Indicator(
                        mode="gauge+number", value=battery, title={'text': "Battery (%)"},
                        gauge={'axis': {'range': [0, 100]}}
                    )), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                ], style={'display': 'flex', 'justifyContent': 'space-around', 'gap': '10px', 'marginTop': '20px'}),
            ], style={'backgroundColor': '#111', 'borderRadius': '20px', 'boxShadow': '0 0 40px #00eaff', 'padding': '20px'}),
        ], style={'padding': '20px'})
    ])
    for name, coords in ports.items():
        G.add_node(name, pos=coords)
    # Add edges with distance as weight
    G.add_edge('A', 'B', weight=700)
    G.add_edge('B', 'C', weight=800)
    G.add_edge('C', 'D', weight=900)
    G.add_edge('A', 'C', weight=1500)
    G.add_edge('B', 'D', weight=1600)
    return G, ports

# Route calculation using Dijkstra's algorithm
def calculate_route(start, end):
    G, ports = build_sea_graph()
    try:
        path = nx.dijkstra_path(G, start, end, weight='weight')
        distance = nx.dijkstra_path_length(G, start, end, weight='weight')
        return path, distance
    except nx.NetworkXNoPath:
        return None, None
app.layout = html.Div([
                    html.Div([
                        html.H3('Radar', style={'color': 'lime', 'textAlign': 'center'}),
                        dcc.Graph(id='radar', figure=radar_figure(), style={'backgroundColor': 'black', 'height': '300px'}),
                    ], style={'flex': '1', 'backgroundColor': '#222', 'margin': '10px', 'borderRadius': '10px', 'padding': '10px'}),
    html.H1('Marine Time Weather Dashboard', style={'textAlign': 'center'}),
    html.Div([
        html.Div([
            dcc.Graph(
                id='speedometer',
                figure=go.Figure(go.Indicator(
                    mode="gauge+number",
                    value=speed,
                    title={'text': "Speed (knots)"},
                    gauge={'axis': {'range': [0, 40]}}
                ))
            ),
            dcc.Graph(
                id='temperature',
                figure=go.Figure(go.Indicator(
                    mode="gauge+number",
                    value=temperature,
                    title={'text': "Temperature (°C)"},
                    gauge={'axis': {'range': [-10, 40]}}
                ))
            ),
            dcc.Graph(
                id='pressure',
                figure=go.Figure(go.Indicator(
                    dcc.Graph(
                        id='engine_temp',
                        figure=go.Figure(go.Indicator(
                            mode="gauge+number",
                            value=engine_temp,
                            title={'text': "Engine Temp (°C)"},
                            gauge={'axis': {'range': [50, 120]}}
                        ))
                    ),
                    dcc.Graph(
                        id='humidity',
                        figure=go.Figure(go.Indicator(
                            mode="gauge+number",
                            value=humidity,
            html.H1('Marine Time Weather Dashboard', style={'textAlign': 'center', 'color': '#00eaff', 'textShadow': '2px 2px 8px #000'}),
            html.Div([
                html.Div([
                    html.H3('Radar', style={'color': 'lime', 'textAlign': 'center', 'textShadow': '1px 1px 6px #000'}),
                    dcc.Graph(id='radar', figure=radar_figure(), style={'backgroundColor': 'black', 'height': '300px', 'boxShadow': '0 0 20px #00eaff'}),
                    html.Div([
                        dcc.Graph(id='speedometer', figure=go.Figure(go.Indicator(mode="gauge+number", value=speed, title={'text': "Speed (knots)"}, gauge={'axis': {'range': [0, 40]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                        dcc.Graph(id='temperature', figure=go.Figure(go.Indicator(mode="gauge+number", value=temperature, title={'text': "Temperature (°C)"}, gauge={'axis': {'range': [-10, 40]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #ff9800'}),
                        dcc.Graph(id='pressure', figure=go.Figure(go.Indicator(mode="gauge+number", value=pressure, title={'text': "Pressure (hPa)"}, gauge={'axis': {'range': [950, 1050]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                        dcc.Graph(id='fan_rpm', figure=go.Figure(go.Indicator(mode="gauge+number", value=fan_rpm, title={'text': "Fan RPM"}, gauge={'axis': {'range': [0, 4000]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                        dcc.Graph(id='compass', figure=go.Figure(go.Indicator(mode="gauge+number", value=compass, title={'text': "Compass (°)"}, gauge={'axis': {'range': [0, 360]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                        dcc.Graph(id='engine_temp', figure=go.Figure(go.Indicator(mode="gauge+number", value=engine_temp, title={'text': "Engine Temp (°C)"}, gauge={'axis': {'range': [50, 120]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #ff9800'}),
                        dcc.Graph(id='humidity', figure=go.Figure(go.Indicator(mode="gauge+number", value=humidity, title={'text': "Humidity (%)"}, gauge={'axis': {'range': [0, 100]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                        dcc.Graph(id='wind_speed', figure=go.Figure(go.Indicator(mode="gauge+number", value=wind_speed, title={'text': "Wind Speed (kt)"}, gauge={'axis': {'range': [0, 50]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                        dcc.Graph(id='battery', figure=go.Figure(go.Indicator(mode="gauge+number", value=battery, title={'text': "Battery (%)"}, gauge={'axis': {'range': [0, 100]}})), style={'backgroundColor': '#222', 'borderRadius': '10px', 'boxShadow': '0 0 10px #00eaff'}),
                    ], style={'display': 'flex', 'justifyContent': 'space-around', 'gap': '10px', 'marginTop': '20px'}),
                ], style={'backgroundColor': '#111', 'borderRadius': '20px', 'boxShadow': '0 0 40px #00eaff', 'padding': '20px'}),
            ], style={'padding': '20px'}),
            if start in ['A', 'B', 'C', 'D'] and end in ['A', 'B', 'C', 'D']:
                path, distance = calculate_route(start, end)
                if path:
                    G, ports = build_sea_graph()
                    weather_data = simulate_weather(path, ports)
                    create_route_map(path, ports)
                    with open(os.path.join(os.path.dirname(__file__), 'folium_map.html'), 'r') as f:
                        map_html = f.read()
                    weather_summary = 'Weather per segment:<br>'
                    for w in weather_data:
                        weather_summary += (f"{w['segment']}: Temp {w['temperature']}°C, "
                                           f"Wind {w['wind']}kt, Pressure {w['pressure']}hPa, "
                                           f"Rainfall {w['rainfall']}mm<br>")
                    route_info = f"Route: {' -> '.join(path)} | Total Distance: {distance} km<br>{weather_summary}"
                    return route_info, map_html
                else:
                    return "No route found.", dash.no_update
            else:
                return "Enter start/end as 'A', 'B', 'C', or 'D' for demo.", dash.no_update
        return "", dash.no_update

    app.run(debug=True)
def radar_figure():
    theta = np.linspace(0, 360, 36)
    r = np.abs(np.sin(np.radians(theta)) * np.random.uniform(0.8, 1.2, size=36))
    frames = [go.Frame(data=[go.Scatterpolar(r=np.abs(np.sin(np.radians(theta + i*10)) * np.random.uniform(0.8, 1.2, size=36)),
                                             theta=theta,
                                             mode='lines',
                                             line=dict(color='lime', width=3))]) for i in range(36)]
    fig = go.Figure(
        data=[go.Scatterpolar(r=r, theta=theta, mode='lines', line=dict(color='lime', width=3))],
        frames=frames
    )
    fig.update_layout(
        polar=dict(bgcolor='black', radialaxis=dict(visible=False)),
        showlegend=False,
        margin=dict(l=20, r=20, t=20, b=20),
        paper_bgcolor='black',
        font=dict(color='lime'),
        updatemenus=[{
            'type': 'buttons',
            'buttons': [
                {
                    'label': 'Radar Sweep',
                    'method': 'animate',
                    'args': [None, {'frame': {'duration': 50, 'redraw': True}, 'fromcurrent': True}]
                }
            ],
            'direction': 'left',
            'pad': {'r': 10, 't': 10},
            'showactive': True
        }]
    )
    return fig